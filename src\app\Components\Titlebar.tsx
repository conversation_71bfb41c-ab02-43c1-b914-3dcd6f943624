import { Link } from "@heroui/react";

export default function Titlebar() {
    return (
        <div className="fixed top-0 left-0 h-25 w-full  z-[1] flex flex-row justify-center items-center">
            <div className="w-[70%] flex flex-row items-center justify-between">
                <div className="w-[100px] h-[100px] bg-white">
                    <h1>LOGO</h1>
                </div>
                <div className="flex flex-row gap-2">
                    <Link href="/" color="foreground" underline="hover">RESTAURACE</Link>
                    <Link href="/" color="foreground" underline="hover">UBYTOVÁNÍ</Link>
                    <Link href="/" color="foreground" underline="hover">UDÁLOSTI</Link>
                    <Link href="/" color="foreground" underline="hover">FOTOGRAFIE</Link>
                    <Link href="/" color="foreground" underline="hover">KONTAKT</Link>
                </div>
            </div>
        </div>
    );
}